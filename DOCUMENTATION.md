# Petrochemicals Catalog Platform

## Overview

The Petrochemicals Catalog Platform is a comprehensive Django-based web application designed to manage and showcase petrochemical solutions, plants, processes, and related business operations. The platform serves as a central hub for petrochemical industry stakeholders to discover solutions, explore facilities, apply for jobs, and engage with the business.

## Key Features

### 🏭 Petrochemicals Catalog
- **Solutions Management**: Catalog of petrochemical solutions with detailed descriptions, categories, and commercial information
- **Plant Directory**: Global directory of petrochemical plants with geographic information, contact details, and facility data
- **Process Management**: Manufacturing processes used in the petrochemical industry
- **Category Hierarchy**: Hierarchical organization of petrochemical categories with image support
- **Geographic Mapping**: Location-based plant ordering and geographic visualization support

### 💼 Job Board
- **Vacancy Management**: Job postings with department categorization and location-based filtering
- **Application System**: Complete job application workflow with resume upload and status tracking
- **Department Organization**: Structured department management for job categorization
- **Application Status Tracking**: Comprehensive status management from pending to offer acceptance

### 👤 User Management & Social Features
- **User Authentication**: JWT-based authentication with registration and login
- **User Profiles**: Comprehensive user profile management with personal and professional information
- **Bookmark System**: Save and manage favorite solutions and plants (supports both authenticated and session-based bookmarking)
- **Contact System**: Contact form with status tracking for customer inquiries

### 📊 Business Intelligence
- **Inquiry Management**: Handle customer inquiries for specific solutions
- **MSDS/TDS Requests**: Manage Material Safety Data Sheet and Technical Data Sheet requests
- **Lead Tracking**: Track potential customers and their interests
- **Geographic Analytics**: Plant distribution and geographic insights

## Technical Architecture

### Technology Stack
- **Backend**: Django 5.2 with Django REST Framework
- **Database**: PostgreSQL with Redis for caching
- **Authentication**: JWT (Simple JWT)
- **API Documentation**: drf-yasg (Swagger/OpenAPI)
- **Rich Text**: Martor (Markdown editor)
- **Containerization**: Docker with Docker Compose
- **Web Server**: Nginx for static files and reverse proxy

### Application Structure

`
├── account/              # User authentication and profile management
├── core/                 # Core models (Location, ContactUs, Image, File)
├── job_board/           # Job postings and application management
├── petrochemicals_catalog/  # Main catalog functionality
├── social/              # Bookmarking and social features
├── infrastructure/      # Django settings, middleware, utilities
├── docs/               # Documentation
├── data/               # Sample data files
├── nginx/              # Nginx configuration
└── static/             # Static files
`

## Core Data Models

### Petrochemicals Catalog Models

#### Solution
- **Purpose**: Represents petrochemical solutions/products
- **Key Fields**: name, commercial_name, description (Markdown), categories (M2M)
- **Features**: Dynamic image resolution from category hierarchy, process relationships

#### Plant
- **Purpose**: Petrochemical processing facilities
- **Key Fields**: name, latitude/longitude, address, country, contact information
- **Features**: Geographic ordering, solution relationships, contact management

#### Process
- **Purpose**: Manufacturing processes
- **Key Fields**: name, solutions (M2M), plants (M2M)
- **Features**: Links solutions to plants through processes

#### Category
- **Purpose**: Hierarchical categorization system
- **Key Fields**: name, parent (self-referential), image
- **Features**: Tree structure, recursive image inheritance

### Business Models

#### Inquiry/MSDS/TDS Requests
- **Purpose**: Customer requests for information
- **Base Class**: BaseSolutionRequest (includes customer details, job function, company info)
- **Features**: Solution-specific requests, marketing consent tracking

#### Job Board Models
- **Vacancy**: Job postings with department and location
- **Application**: Job applications with status tracking and file uploads
- **Department**: Organizational structure for job categorization

### Core Models

#### Location
- **Purpose**: Geographic hierarchy (continent → country → state → city → suburb)
- **Features**: Self-referential hierarchy, type-based filtering

#### User
- **Purpose**: Custom user model with extended profile information
- **Features**: Email-based authentication, profile images, occupation tracking

#### ContactUs
- **Purpose**: Customer contact form submissions
- **Features**: Status tracking, location association

## API Endpoints

### Petrochemicals Catalog API
`
GET /api/v1/petrochemicals_catalog/categories/     # List categories
GET /api/v1/petrochemicals_catalog/solutions/      # List solutions
GET /api/v1/petrochemicals_catalog/plants/         # List plants
GET /api/v1/petrochemicals_catalog/processes/      # List processes

# Solution-specific actions
POST /api/v1/petrochemicals_catalog/solutions/{id}/inquiry/
POST /api/v1/petrochemicals_catalog/solutions/{id}/msds_code_request/
POST /api/v1/petrochemicals_catalog/solutions/{id}/tds_code_request/
`

### Job Board API
`
GET /api/v1/job_board/departments/     # List departments
GET /api/v1/job_board/vacancies/       # List job vacancies
POST /api/v1/job_board/applications/   # Submit job application
`

### Core API
`
GET /api/v1/core/locations/            # List locations
POST /api/v1/core/contact_us/          # Submit contact form
`

### Authentication API
`
POST /api/v1/account/user-register/    # User registration
POST /api/v1/account/login/            # User login
POST /api/v1/account/rotate-token/     # Refresh JWT token
GET /api/v1/account/user-profile/      # Get user profile
PUT /api/v1/account/user-profile/      # Update user profile
`

### Social Features API
`
POST /api/v1/petrochemicals_catalog/solutions/{id}/bookmark/    # Bookmark solution
DELETE /api/v1/petrochemicals_catalog/solutions/{id}/bookmark/  # Remove bookmark
GET /api/v1/petrochemicals_catalog/solutions/bookmarks/        # List bookmarked solutions
`

## Installation & Setup

### Docker Setup (Recommended)

1. Clone the repository:
   `ash
   git clone <repository-url>
   cd first
   `

2. Create environment file:
   `ash
   cp .env.docker .env
   `

3. Build and start containers:
   `ash
   docker-compose up -d --build
   `

4. Access the application:
   - Web application: http://localhost:8383
   - API documentation: http://localhost:8383/swagger/

### Local Development

1. Create virtual environment:
   `ash
   python -m venv .venv
   source .venv/bin/activate  # Windows: .venv\Scripts\activate
   `

2. Install dependencies:
   `ash
   pip install -r requirements.txt
   `

3. Set up environment variables:
   `ash
   cp .env.example .env
   `

4. Run migrations:
   `ash
   python manage.py migrate
   `

5. Seed database:
   `ash
   python manage.py seed_petrochemicals
   `

6. Start development server:
   `ash
   python manage.py runserver
   `

## Management Commands

### Database Seeding
`ash
python manage.py seed_petrochemicals --categories 15 --products 50 --plants 10 --processes 25
python manage.py seed_locations
`

### User Management
`ash
python manage.py createsuperuser
`

### Docker Commands
`ash
docker-compose logs -f                              # View logs
docker-compose exec web python manage.py migrate   # Run migrations
docker-compose exec web python manage.py shell     # Django shell
docker-compose down -v                              # Stop and remove volumes
`

## API Documentation

The platform provides comprehensive API documentation through Swagger/OpenAPI:
- **Swagger UI**: http://localhost:8383/swagger/
- **ReDoc**: http://localhost:8383/redoc/
- **JSON Schema**: http://localhost:8383/swagger.json

## Advanced Features

### Geographic Ordering
Plants can be ordered geographically (west to east by longitude) when the geographic_order=true parameter is used, enabling map-based visualizations.

### Bookmark System
- **Authenticated Users**: Bookmarks stored in database with user association
- **Anonymous Users**: Bookmarks stored in session for temporary access
- **Session Transfer**: Anonymous bookmarks transferred to user account upon login

### Filtering & Search
- **Solution Filtering**: By categories, processes, and plants
- **Plant Filtering**: By country and solution availability
- **Search**: Full-text search on solution names
- **Location Filtering**: Hierarchical location filtering with plant availability

### Request Management
The platform handles three types of solution-related requests:
1. **General Inquiries**: Open-ended questions about solutions
2. **MSDS Requests**: Material Safety Data Sheet requests
3. **TDS Requests**: Technical Data Sheet requests

All requests capture detailed customer information including job function, company details, and marketing preferences.

## Business Logic

### Job Function Tracking
The platform tracks detailed job functions for lead qualification:
- Executive/Director levels
- Technical roles (Technology Manager, R&D, Engineering)
- Operational roles (Plant Management, Purchasing, Logistics)
- Business roles (Sales, Business Development, Finance)
- External stakeholders (Consultants, Press, Suppliers, Students)

### Status Management
- **Job Applications**: 10-stage workflow from pending to offer acceptance/decline
- **Contact Inquiries**: 4-stage workflow (new → in progress → resolved → closed)
- **Solution Requests**: Automatic tracking with timestamp and user association

### Data Relationships
The platform maintains complex relationships:
- Solutions ↔ Categories (Many-to-Many)
- Solutions ↔ Processes (Many-to-Many)
- Processes ↔ Plants (Many-to-Many)
- Plants → Countries (Foreign Key)
- Categories → Parent Categories (Self-referential)
- Users → Bookmarks → Any Model (Generic Foreign Key)

## Testing

Run the test suite:
`ash
python manage.py test
`

For specific app testing:
`ash
python manage.py test petrochemicals_catalog
python manage.py test job_board
python manage.py test account
`

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Run the test suite
6. Submit a pull request

## License

[Add your license information here]
