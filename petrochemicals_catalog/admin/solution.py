from django import forms
from django.contrib import admin
from django.contrib.admin.widgets import FilteredSelectMultiple

from petrochemicals_catalog.models import Solution, Plant, Process


class SolutionAdminForm(forms.ModelForm):
    # plants = forms.ModelMultipleChoiceField(
    #     queryset=Plant.objects.all(),
    #     required=False,
    #     widget=FilteredSelectMultiple(
    #         verbose_name='Plants',
    #         is_stacked=False
    #     )
    # )

    processes = forms.ModelMultipleChoiceField(
        queryset=Process.objects.all(),
        required=False,
        widget=FilteredSelectMultiple(
            verbose_name='Processes',
            is_stacked=False
        )
    )

    class Meta:
        model = Solution
        fields = '__all__'

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        if self.instance.pk:
            # self.fields['plants'].initial = self.instance.plants.all()
            self.fields['processes'].initial = self.instance.processes.all()

    def save(self, commit=True):
        solution = super().save(commit=commit)

        # Save the plants and processes
        if 'plants' in self.cleaned_data and 'processes' in self.cleaned_data:
            plants = self.cleaned_data['plants']
            processes = self.cleaned_data['processes']

            # If we're committing right now, set the relationships
            if commit:
                solution.plants.set(plants)
                solution.processes.set(processes)
            # If we're not committing, add a method to be called when the instance is saved
            else:
                old_save_m2m = self.save_m2m

                def save_m2m():
                    old_save_m2m()
                    solution.plants.set(plants)
                    solution.processes.set(processes)

                self.save_m2m = save_m2m

        return solution


@admin.register(Solution)
class SolutionAdmin(admin.ModelAdmin):
    form = SolutionAdminForm
    list_display = ["name", "commercial_name"]
    list_filter = ['categories']
    filter_horizontal = ["categories"]
    search_fields = ["name", "commercial_name", "description", "categories__name"]
