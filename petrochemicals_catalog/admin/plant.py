from django import forms
from django.contrib import admin
from django.contrib.admin.widgets import FilteredSelectMultiple

from petrochemicals_catalog.models import Plant, Process


class PlantAdminForm(forms.ModelForm):
    processes = forms.ModelMultipleChoiceField(
        queryset=Process.objects.all(),
        required=False,
        widget=FilteredSelectMultiple(
            verbose_name='Processes',
            is_stacked=False
        )
    )

    class Meta:
        model = Plant
        fields = '__all__'

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        if self.instance.pk:
            self.fields['processes'].initial = self.instance.processes.all()

    def save(self, commit=True):
        plant = super().save(commit=commit)

        # Save the processes
        if 'processes' in self.cleaned_data:
            processes = self.cleaned_data['processes']

            # If we're committing right now, set the processes
            if commit:
                plant.processes.set(processes)
            # If we're not committing, add a method to be called when the instance is saved
            else:
                old_save_m2m = self.save_m2m

                def save_m2m():
                    old_save_m2m()
                    plant.processes.set(processes)

                self.save_m2m = save_m2m

        return plant


@admin.register(Plant)
class PlantAdmin(admin.ModelAdmin):
    form = PlantAdminForm
    list_display = ["name"]
    exclude = ("deprecated_solutions",)
    raw_id_fields = ("country",)
    search_fields = [
        "name",
        "address",
        "country__name",
        "processes__name",
        "phone_number",
        "email",
        "website_url",
        "linkedin_url",
    ]
