# Generated by Django 5.2 on 2025-06-07 10:04

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('petrochemicals_catalog', '0004_alter_plant_email_alter_plant_phone_number_and_more'),
    ]

    operations = [
        migrations.RenameField(
            model_name='plant',
            old_name='solutions',
            new_name='deprecated_solutions',
        ),
        migrations.AlterField(
            model_name='plant',
            name='deprecated_solutions',
            field=models.ManyToManyField(blank=True, db_table='petrochemicals_catalog_plant_solutions', related_name='deprecated_plants', to='petrochemicals_catalog.solution'),
        ),
    ]
