from django.urls import reverse
from rest_framework import status
from rest_framework.test import APITestCase

from core.models import Location
from petrochemicals_catalog.models import (
    Category, Process, Plant, Solution,
    Inquiry, MSDSCodeRequest, TDSCodeRequest
)
from petrochemicals_catalog.models.base_solution_request import JobFunctionChoices


class BaseAPITestCase(APITestCase):
    def setUp(self):
        """Set up test data that will be used across multiple test cases"""
        # Create test country
        self.country = Location.objects.create(
            name="Test Country",
            type="country"
        )

        # Create test categories
        self.parent_category = Category.objects.create(name="Parent Category")
        self.child_category = Category.objects.create(
            name="Child Category",
            parent=self.parent_category
        )

        # Create test solution
        self.solution = Solution.objects.create(
            name="Test Solution",
            commercial_name="Commercial Test Solution",
            description="Test description"
        )
        self.solution.categories.add(self.child_category)

        # Create test process
        self.process = Process.objects.create(name="Test Process")
        self.process.solutions.add(self.solution)

        # Create test plant
        self.plant = Plant.objects.create(
            name="Test Plant",
            country=self.country,
            latitude=0.0,
            longitude=0.0
        )
        self.plant.processes.add(self.process)
        self.plant.solutions.add(self.solution)


class CategoryAPITest(BaseAPITestCase):
    def setUp(self):
        super().setUp()
        self.url = reverse('categories-list')

    def test_list_categories(self):
        """Test listing all categories"""
        response = self.client.get(self.url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data), 1)  # Only parent categories
        self.assertEqual(response.data[0]['name'], "Parent Category")

    def test_list_categories_with_solutions(self):
        """Test listing categories that have solutions"""
        response = self.client.get(f"{self.url}?has_solution=true")
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data), 1)
        self.assertEqual(response.data[0]['name'], "Parent Category")

    def test_retrieve_category(self):
        """Test retrieving a single category"""
        url = reverse('categories-detail', args=[self.parent_category.id])
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['name'], "Parent Category")
        self.assertEqual(len(response.data['children']), 1)


class ProcessAPITest(BaseAPITestCase):
    def setUp(self):
        super().setUp()
        self.url = reverse('processes-list')

    def test_list_processes(self):
        """Test listing all processes"""
        response = self.client.get(self.url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data), 1)
        self.assertEqual(response.data[0]['name'], "Test Process")

    def test_list_processes_with_solutions(self):
        """Test listing processes that have solutions"""
        response = self.client.get(f"{self.url}?has_solution=true")
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data), 1)

    def test_list_processes_with_plants(self):
        """Test listing processes that have plants"""
        response = self.client.get(f"{self.url}?has_plant=true")
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data), 1)


class PlantAPITest(BaseAPITestCase):
    def setUp(self):
        super().setUp()
        self.url = reverse('plants-list')

    def test_list_plants(self):
        """Test listing all plants"""
        response = self.client.get(self.url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data['results']), 1)
        self.assertEqual(response.data['results'][0]['name'], "Test Plant")

    def test_list_plants_geographic_order(self):
        """Test listing plants in geographic order"""
        response = self.client.get(f"{self.url}?geographic_order=true")
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data['results']), 1)

    def test_retrieve_plant(self):
        """Test retrieving a single plant with details"""
        url = reverse('plants-detail', args=[self.plant.id])
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['name'], "Test Plant")
        self.assertEqual(len(response.data['solutions']), 1)


class SolutionAPITest(BaseAPITestCase):
    def setUp(self):
        super().setUp()
        self.url = reverse('solutions-list')
        self.solution_url = reverse('solutions-detail', args=[self.solution.id])
        self.base_request_data = {
            'first_name': 'John',
            'last_name': 'Doe',
            'company_email_address': '<EMAIL>',
            'job_function': JobFunctionChoices.technology_manager,
            'company': 'Test Company',
            'country': self.country.id,
            'has_agreed_to_receive_marketing_messages': True
        }

    def test_list_solutions(self):
        """Test listing all solutions"""
        response = self.client.get(self.url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data['results']), 1)
        self.assertEqual(response.data['results'][0]['name'], "Test Solution")

    def test_retrieve_solution(self):
        """Test retrieving a single solution"""
        response = self.client.get(self.solution_url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['name'], "Test Solution")

    def test_submit_inquiry(self):
        """Test submitting an inquiry for a solution"""
        url = reverse('solutions-inquiry', args=[self.solution.id])
        data = self.base_request_data.copy()
        data['message'] = 'Test inquiry message'

        response = self.client.post(url, data, format='json')
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertTrue(Inquiry.objects.filter(solution=self.solution).exists())

    def test_request_msds_code(self):
        """Test requesting MSDS code for a solution"""
        url = reverse('solutions-msds-code-request', args=[self.solution.id])

        response = self.client.post(url, self.base_request_data, format='json')
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertTrue(MSDSCodeRequest.objects.filter(solution=self.solution).exists())

    def test_request_tds_code(self):
        """Test requesting TDS code for a solution"""
        url = reverse('solutions-tds-code-request', args=[self.solution.id])

        response = self.client.post(url, self.base_request_data, format='json')
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertTrue(TDSCodeRequest.objects.filter(solution=self.solution).exists())

    def test_get_job_functions(self):
        """Test getting list of job functions"""
        url = reverse('solutions-job-functions')
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data), len(JobFunctionChoices.choices))
        self.assertTrue(all('id' in item and 'name' in item for item in response.data))

    def test_invalid_inquiry_data(self):
        """Test submitting inquiry with invalid data"""
        url = reverse('solutions-inquiry', args=[self.solution.id])
        data = self.base_request_data.copy()
        data['company_email_address'] = 'invalid-email'  # Invalid email

        response = self.client.post(url, data, format='json')
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

    def test_invalid_msds_request_data(self):
        """Test submitting MSDS request with invalid data"""
        url = reverse('solutions-msds-code-request', args=[self.solution.id])
        data = self.base_request_data.copy()
        del data['first_name']  # Missing required field

        response = self.client.post(url, data, format='json')
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

    def test_invalid_tds_request_data(self):
        """Test submitting TDS request with invalid data"""
        url = reverse('solutions-tds-code-request', args=[self.solution.id])
        data = self.base_request_data.copy()
        data['job_function'] = 'invalid_choice'  # Invalid job function

        response = self.client.post(url, data, format='json')
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
