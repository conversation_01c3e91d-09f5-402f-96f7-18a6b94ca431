from django.test import TestCase
from django.core.exceptions import ValidationError
from django.db import IntegrityError
from petrochemicals_catalog.models import Process, Solution, Plant
from core.models import Location


class ProcessModelTest(TestCase):
    def setUp(self):
        """Set up test data"""
        self.country = Location.objects.create(name="Test Country", type="country")
        self.process = Process.objects.create(name="Test Process")
        self.solution = Solution.objects.create(
            name="Test Solution",
            commercial_name="Commercial Test Solution",
            description="Test description"
        )
        self.plant = Plant.objects.create(
            name="Test Plant",
            latitude=0.0,
            longitude=0.0,
            country=self.country,
            website_url="https://test.com"
        )

    def test_process_creation(self):
        """Test basic process creation"""
        self.assertEqual(self.process.name, "Test Process")
        self.assertEqual(self.process.solutions.count(), 0)
        self.assertEqual(self.process.plants.count(), 0)

    def test_process_unique_name(self):
        """Test that process names must be unique"""
        with self.assertRaises(IntegrityError):
            Process.objects.create(name="Test Process")

    def test_process_str_representation(self):
        """Test string representation of process"""
        self.assertEqual(str(self.process), "Test Process")

    def test_process_solution_relationship(self):
        """Test process-solution relationship"""
        self.process.solutions.add(self.solution)
        self.assertEqual(self.process.solutions.count(), 1)
        self.assertEqual(self.process.solutions.first(), self.solution)
        self.assertEqual(self.solution.processes.count(), 1)
        self.assertEqual(self.solution.processes.first(), self.process)

    def test_process_plant_relationship(self):
        """Test process-plant relationship"""
        self.process.plants.add(self.plant)
        self.assertEqual(self.process.plants.count(), 1)
        self.assertEqual(self.process.plants.first(), self.plant)
        self.assertEqual(self.plant.processes.count(), 1)
        self.assertEqual(self.plant.processes.first(), self.process)

    def test_process_with_multiple_solutions(self):
        """Test process with multiple solutions"""
        solution2 = Solution.objects.create(
            name="Test Solution 2",
            commercial_name="Commercial Test Solution 2",
            description="Test description 2"
        )
        self.process.solutions.add(self.solution, solution2)
        self.assertEqual(self.process.solutions.count(), 2)
        self.assertIn(self.solution, self.process.solutions.all())
        self.assertIn(solution2, self.process.solutions.all())

    def test_process_with_multiple_plants(self):
        """Test process with multiple plants"""
        country2 = Location.objects.create(name="Test Country 2", type="country")
        plant2 = Plant.objects.create(
            name="Test Plant 2",
            latitude=1.0,
            longitude=1.0,
            country=country2,
            website_url="https://test2.com"
        )
        self.process.plants.add(self.plant, plant2)
        self.assertEqual(self.process.plants.count(), 2)
        self.assertIn(self.plant, self.process.plants.all())
        self.assertIn(plant2, self.process.plants.all())

    def test_process_verbose_names(self):
        """Test process model verbose names"""
        self.assertEqual(Process._meta.verbose_name, "Process")
        self.assertEqual(Process._meta.verbose_name_plural, "Processes")

    def test_process_blank_relationships(self):
        """Test that process can be created without solutions or plants"""
        process = Process.objects.create(name="Empty Process")
        self.assertEqual(process.solutions.count(), 0)
        self.assertEqual(process.plants.count(), 0)

    def test_process_remove_solution(self):
        """Test removing a solution from a process"""
        self.process.solutions.add(self.solution)
        self.process.solutions.remove(self.solution)
        self.assertEqual(self.process.solutions.count(), 0)
        self.assertEqual(self.solution.processes.count(), 0)

    def test_process_remove_plant(self):
        """Test removing a plant from a process"""
        self.process.plants.add(self.plant)
        self.process.plants.remove(self.plant)
        self.assertEqual(self.process.plants.count(), 0)
        self.assertEqual(self.plant.processes.count(), 0) 