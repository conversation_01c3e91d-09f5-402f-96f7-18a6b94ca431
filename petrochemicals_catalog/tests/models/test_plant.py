from django.core.exceptions import ValidationError
from django.db.models import ProtectedError
from django.test import TestCase
from petrochemicals_catalog.models import Plant, Solution, Process
from core.models import Location


class PlantModelTest(TestCase):
    def setUp(self):
        """Set up test data"""
        self.country = Location.objects.create(
            name="Test Country",
            type="country"
        )
        self.plant = Plant.objects.create(
            name="Test Plant",
            latitude=0.0,
            longitude=0.0,
            address="Test plant address",
            country=self.country,
            website_url="https://test.com",
            email="<EMAIL>",
            phone_number="+1234567890",
            linkedin_url="https://linkedin.com/test"
        )
        self.solution = Solution.objects.create(
            name="Test Solution",
            commercial_name="Commercial Test Solution",
            description="Test description"
        )
        self.process = Process.objects.create(name="Test Process")

    def test_plant_creation(self):
        """Test basic plant creation"""
        self.assertEqual(self.plant.name, "Test Plant")
        self.assertEqual(self.plant.latitude, 0.0)
        self.assertEqual(self.plant.longitude, 0.0)
        self.assertEqual(self.plant.address, "Test plant address")
        self.assertEqual(self.plant.country, self.country)
        self.assertEqual(self.plant.website_url, "https://test.com")
        self.assertEqual(self.plant.email, "<EMAIL>")
        self.assertEqual(self.plant.phone_number, "+1234567890")
        self.assertEqual(self.plant.linkedin_url, "https://linkedin.com/test")
        self.assertEqual(self.plant.solutions.count(), 0)
        self.assertEqual(self.plant.processes.count(), 0)

    def test_plant_str_representation(self):
        """Test string representation of plant"""
        self.assertEqual(str(self.plant), "Test Plant")

    def test_plant_solution_relationship(self):
        """Test plant-solution relationship"""
        self.plant.solutions.add(self.solution)
        self.assertEqual(self.plant.solutions.count(), 1)
        self.assertEqual(self.plant.solutions.first(), self.solution)
        self.assertEqual(self.solution.plants.count(), 1)
        self.assertEqual(self.solution.plants.first(), self.plant)

    def test_plant_process_relationship(self):
        """Test plant-process relationship"""
        self.plant.processes.add(self.process)
        self.assertEqual(self.plant.processes.count(), 1)
        self.assertEqual(self.plant.processes.first(), self.process)
        self.assertEqual(self.process.plants.count(), 1)
        self.assertEqual(self.process.plants.first(), self.plant)

    def test_plant_with_multiple_solutions(self):
        """Test plant with multiple solutions"""
        solution2 = Solution.objects.create(
            name="Test Solution 2",
            commercial_name="Commercial Test Solution 2",
            description="Test description 2"
        )
        self.plant.solutions.add(self.solution, solution2)
        self.assertEqual(self.plant.solutions.count(), 2)
        self.assertIn(self.solution, self.plant.solutions.all())
        self.assertIn(solution2, self.plant.solutions.all())

    def test_plant_with_multiple_processes(self):
        """Test plant with multiple processes"""
        process2 = Process.objects.create(name="Test Process 2")
        self.plant.processes.add(self.process, process2)
        self.assertEqual(self.plant.processes.count(), 2)
        self.assertIn(self.process, self.plant.processes.all())
        self.assertIn(process2, self.plant.processes.all())

    def test_plant_country_protection(self):
        """Test that plant's country cannot be deleted"""
        with self.assertRaises(ProtectedError):
            self.country.delete()

    def test_plant_required_fields(self):
        """Test plant required fields"""
        required_fields = ['name', 'latitude', 'longitude', 'country', 'website_url']
        for field in required_fields:
            data = {
                'name': 'Test Plant',
                'latitude': 0.0,
                'longitude': 0.0,
                'country': self.country,
                'website_url': 'https://test.com'
            }
            del data[field]
            with self.assertRaises(ValidationError):
                plant = Plant(**data)
                plant.full_clean()

    def test_plant_optional_fields(self):
        """Test plant optional fields"""
        plant = Plant.objects.create(
            name="Optional Fields Plant",
            latitude=0.0,
            longitude=0.0,
            country=self.country,
            website_url="https://test.com"
        )
        self.assertIsNone(plant.email)
        self.assertIsNone(plant.phone_number)
        self.assertIsNone(plant.linkedin_url)
        self.assertEqual(plant.address, "")

    def test_plant_invalid_email(self):
        """Test plant with invalid email"""
        plant = Plant(
            name="Invalid Email Plant",
            latitude=0.0,
            longitude=0.0,
            country=self.country,
            website_url="https://test.com",
            email="invalid-email"
        )
        with self.assertRaises(ValidationError):
            plant.full_clean()

    def test_plant_invalid_website_url(self):
        """Test plant with invalid website URL"""
        plant = Plant(
            name="Invalid URL Plant",
            latitude=0.0,
            longitude=0.0,
            country=self.country,
            website_url="invalid-url"
        )
        with self.assertRaises(ValidationError):
            plant.full_clean()

    def test_plant_invalid_linkedin_url(self):
        """Test plant with invalid LinkedIn URL"""
        plant = Plant(
            name="Invalid LinkedIn Plant",
            latitude=0.0,
            longitude=0.0,
            country=self.country,
            website_url="https://test.com",
            linkedin_url="invalid-url"
        )
        with self.assertRaises(ValidationError):
            plant.full_clean()

    def test_plant_remove_solution(self):
        """Test removing a solution from a plant"""
        self.plant.solutions.add(self.solution)
        self.plant.solutions.remove(self.solution)
        self.assertEqual(self.plant.solutions.count(), 0)
        self.assertEqual(self.solution.plants.count(), 0)

    def test_plant_remove_process(self):
        """Test removing a process from a plant"""
        self.plant.processes.add(self.process)
        self.plant.processes.remove(self.process)
        self.assertEqual(self.plant.processes.count(), 0)
        self.assertEqual(self.process.plants.count(), 0) 