from django.core.exceptions import ValidationError
from django.db import IntegrityError
from django.test import TestCase

from core.models import Image, Location
from petrochemicals_catalog.models import Solution, Category, Plant, Process


class SolutionModelTest(TestCase):
    def setUp(self):
        """Set up test data"""
        # Create test images
        self.default_image = Image.objects.create(
            image="default.jpg",
            alt="Default Refinery"
        )
        self.category_image = Image.objects.create(
            image="category.jpg",
            alt="Category Image"
        )
        self.parent_category_image = Image.objects.create(
            image="parent.jpg",
            alt="Parent Category Image"
        )

        # Create test categories
        self.parent_category = Category.objects.create(
            name="Parent Category",
            image=self.parent_category_image
        )
        self.child_category = Category.objects.create(
            name="Child Category",
            parent=self.parent_category,
            image=self.category_image
        )
        self.category_without_image = Category.objects.create(
            name="No Image Category",
            parent=self.parent_category
        )

        # Create test solution
        self.solution = Solution.objects.create(
            name="Test Solution",
            commercial_name="Commercial Test Solution",
            description="Test description"
        )
        self.solution.categories.add(self.child_category)

        # Create test country
        self.country = Location.objects.create(name="Test Country", type="country")

        # Create test plant and process
        self.plant = Plant.objects.create(
            name="Test Plant",
            latitude=0.0,
            longitude=0.0,
            country=self.country,
            website_url="https://test.com"
        )
        self.process = Process.objects.create(name="Test Process")

    def test_solution_creation(self):
        """Test basic solution creation"""
        self.assertEqual(self.solution.name, "Test Solution")
        self.assertEqual(self.solution.commercial_name, "Commercial Test Solution")
        self.assertEqual(self.solution.description, "Test description")
        self.assertEqual(self.solution.categories.count(), 1)
        self.assertEqual(self.solution.plants.count(), 0)
        self.assertEqual(self.solution.processes.count(), 0)

    def test_solution_str_representation(self):
        """Test string representation of solution"""
        expected_str = f"{self.solution.id}|{self.solution.name}|{self.solution.commercial_name}"
        self.assertEqual(str(self.solution), expected_str)

    def test_solution_category_relationship(self):
        """Test solution-category relationship"""
        self.assertEqual(self.solution.categories.count(), 1)
        self.assertEqual(self.solution.categories.first(), self.child_category)
        self.assertIn(self.solution, self.child_category.solution_set.all())

    def test_solution_plant_relationship(self):
        """Test solution-plant relationship"""
        self.solution.plants.add(self.plant)
        self.assertEqual(self.solution.plants.count(), 1)
        self.assertEqual(self.solution.plants.first(), self.plant)
        self.assertEqual(self.plant.solutions.count(), 1)
        self.assertEqual(self.plant.solutions.first(), self.solution)

    def test_solution_process_relationship(self):
        """Test solution-process relationship"""
        self.solution.processes.add(self.process)
        self.assertEqual(self.solution.processes.count(), 1)
        self.assertEqual(self.solution.processes.first(), self.process)
        self.assertEqual(self.process.solutions.count(), 1)
        self.assertEqual(self.process.solutions.first(), self.solution)

    def test_solution_with_multiple_categories(self):
        """Test solution with multiple categories"""
        self.solution.categories.add(self.parent_category)
        self.assertEqual(self.solution.categories.count(), 2)
        self.assertIn(self.child_category, self.solution.categories.all())
        self.assertIn(self.parent_category, self.solution.categories.all())

    def test_solution_with_multiple_plants(self):
        """Test solution with multiple plants"""
        country2 = Location.objects.create(name="Test Country 2", type="country")
        plant2 = Plant.objects.create(
            name="Test Plant 2",
            latitude=1.0,
            longitude=1.0,
            country=country2,
            website_url="https://test2.com"
        )
        self.solution.plants.add(self.plant, plant2)
        self.assertEqual(self.solution.plants.count(), 2)
        self.assertIn(self.plant, self.solution.plants.all())
        self.assertIn(plant2, self.solution.plants.all())

    def test_solution_with_multiple_processes(self):
        """Test solution with multiple processes"""
        process2 = Process.objects.create(name="Test Process 2")
        self.solution.processes.add(self.process, process2)
        self.assertEqual(self.solution.processes.count(), 2)
        self.assertIn(self.process, self.solution.processes.all())
        self.assertIn(process2, self.solution.processes.all())

    def test_solution_image_inheritance(self):
        """Test solution image inheritance from categories"""
        # Test with child category image
        self.assertEqual(self.solution.image, self.category_image)

        # Test with parent category image when child has no image
        self.solution.categories.remove(self.child_category)
        self.solution.categories.add(self.category_without_image)
        self.assertEqual(self.solution.image, self.parent_category_image)

        # Test with default image when no category has image
        self.solution.categories.clear()
        self.assertEqual(self.solution.image, self.default_image)

    def test_solution_required_fields(self):
        """Test solution required fields"""
        required_fields = ['name', 'commercial_name', 'description']
        for field in required_fields:
            data = {
                'name': 'Test Solution',
                'commercial_name': 'Commercial Test Solution',
                'description': 'Test description'
            }
            del data[field]
            with self.assertRaises(ValidationError):
                solution = Solution(**data)
                solution.full_clean()

    def test_solution_blank_categories(self):
        """Test solution without categories"""
        solution = Solution.objects.create(
            name="No Categories Solution",
            commercial_name="Commercial No Categories Solution",
            description="Test description"
        )
        self.assertEqual(solution.categories.count(), 0)
        self.assertEqual(solution.image, self.default_image)

    def test_solution_remove_category(self):
        """Test removing a category from a solution"""
        self.solution.categories.remove(self.child_category)
        self.assertEqual(self.solution.categories.count(), 0)
        self.assertEqual(self.child_category.solution_set.count(), 0)

    def test_solution_remove_plant(self):
        """Test removing a plant from a solution"""
        self.solution.plants.add(self.plant)
        self.solution.plants.remove(self.plant)
        self.assertEqual(self.solution.plants.count(), 0)
        self.assertEqual(self.plant.solutions.count(), 0)

    def test_solution_remove_process(self):
        """Test removing a process from a solution"""
        self.solution.processes.add(self.process)
        self.solution.processes.remove(self.process)
        self.assertEqual(self.solution.processes.count(), 0)
        self.assertEqual(self.process.solutions.count(), 0) 