from django.db import IntegrityError
from django.db.models import ProtectedError
from django.test import TestCase

from core.models import Image
from petrochemicals_catalog.models import Category


class CategoryModelTest(TestCase):
    def setUp(self):
        """Set up test data"""
        self.image = Image.objects.create(
            image="test.jpg",
            alt="Test Image"
        )
        self.parent_category = Category.objects.create(
            name="Parent Category",
            image=self.image
        )
        self.child_category = Category.objects.create(
            name="Child Category",
            parent=self.parent_category
        )

    def test_category_creation(self):
        """Test basic category creation"""
        self.assertEqual(self.parent_category.name, "Parent Category")
        self.assertEqual(self.parent_category.image, self.image)
        self.assertIsNone(self.parent_category.parent)

        self.assertEqual(self.child_category.name, "Child Category")
        self.assertEqual(self.child_category.parent, self.parent_category)

    def test_category_unique_name(self):
        """Test that category names must be unique"""
        with self.assertRaises(IntegrityError):
            Category.objects.create(name="Parent Category")

    def test_category_str_representation(self):
        """Test string representation of category"""
        self.assertEqual(str(self.parent_category), "Parent Category")
        self.assertEqual(str(self.child_category), "Child Category")

    def test_category_children_relationship(self):
        """Test parent-child relationship"""
        self.assertEqual(self.parent_category.children.count(), 1)
        self.assertEqual(self.parent_category.children.first(), self.child_category)
        self.assertEqual(self.child_category.parent, self.parent_category)

    def test_category_image_inheritance(self):
        """Test that child categories can access parent's image"""
        self.assertIsNone(self.child_category.image)
        self.assertEqual(self.parent_category.image, self.image)

    def test_category_protection_on_delete(self):
        """Test that parent category cannot be deleted if it has children"""
        with self.assertRaises(ProtectedError):
            self.parent_category.delete()

    def test_category_image_protection(self):
        """Test that category image cannot be deleted if it's used by a category"""
        with self.assertRaises(ProtectedError):
            self.image.delete()

    def test_category_without_image(self):
        """Test creating category without image"""
        category = Category.objects.create(name="No Image Category")
        self.assertIsNone(category.image)

    def test_category_without_parent(self):
        """Test creating category without parent"""
        category = Category.objects.create(name="No Parent Category")
        self.assertIsNone(category.parent)

    def test_category_verbose_names(self):
        """Test category model verbose names"""
        self.assertEqual(Category._meta.verbose_name, "Category")
        self.assertEqual(Category._meta.verbose_name_plural, "Categories")
