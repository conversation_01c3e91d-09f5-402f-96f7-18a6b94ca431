from datetime import datetime, timezone

from django.core.exceptions import ValidationError
from django.db import IntegrityError
from django.db.models import ProtectedError
from django.test import TestCase

from core.models import Location
from petrochemicals_catalog.models import Solution, MSDSCodeRequest, TDSCodeRequest, Inquiry
from petrochemicals_catalog.models.base_solution_request import JobFunctionChoices, BaseSolutionRequest


class BaseSolutionRequestTest(TestCase):
    def setUp(self):
        """Set up test data"""
        self.country = Location.objects.create(
            name="Test Country",
            type="country"
        )
        self.solution = Solution.objects.create(
            name="Test Solution",
            commercial_name="Commercial Test Solution",
            description="Test description"
        )
        self.base_data = {
            'first_name': '<PERSON>',
            'last_name': 'Doe',
            'company_email_address': '<EMAIL>',
            'job_function': JobFunctionChoices.technology_manager,
            'company': 'Test Company',
            'country': self.country,
            'solution': self.solution,
            'has_agreed_to_receive_marketing_messages': True
        }

    def test_required_fields(self):
        """Test that required fields raise validation error when missing"""
        required_fields = [
            'first_name', 'last_name', 'company_email_address',
            'job_function', 'company', 'country', 'solution'
        ]
        
        for field in required_fields:
            data = self.base_data.copy()
            del data[field]
            
            request = MSDSCodeRequest(**data)
            with self.assertRaises(ValidationError):
                request.full_clean()

    def test_field_max_lengths(self):
        """Test field max length validation"""
        long_string = 'x' * 256  # Exceeds max_length of 255
        
        fields_to_test = ['first_name', 'last_name', 'company']
        
        for field in fields_to_test:
            data = self.base_data.copy()
            data[field] = long_string
            
            request = MSDSCodeRequest(**data)
            with self.assertRaises(ValidationError):
                request.full_clean()

    def test_invalid_email_validation(self):
        """Test invalid email validation"""
        invalid_emails = [
            'invalid.email',
            '@nodomain.com',
            'no@domain',
            'spaces <EMAIL>'
        ]
        
        for email in invalid_emails:
            data = self.base_data.copy()
            data['company_email_address'] = email
            
            request = MSDSCodeRequest(**data)
            with self.assertRaises(ValidationError):
                request.full_clean()

    def test_job_function_choices_validation(self):
        """Test job function choices validation"""
        data = self.base_data.copy()
        data['job_function'] = 'invalid_choice'
        
        request = MSDSCodeRequest(**data)
        with self.assertRaises(ValidationError):
            request.full_clean()

    def test_created_at_auto_now_add(self):
        """Test created_at is automatically set"""
        request = MSDSCodeRequest.objects.create(**self.base_data)
        self.assertIsInstance(request.created_at, datetime)
        self.assertLessEqual(
            request.created_at.replace(tzinfo=timezone.utc),
            datetime.now(timezone.utc)
        )

    def test_str_representation_format(self):
        """Test string representation format is correct"""
        request = MSDSCodeRequest.objects.create(**self.base_data)
        expected_str = (f'John Doe - Test Solution: Test Company - '
                       f'{request.created_at.strftime("%Y-%m-%d %H:%M:%S")}')
        self.assertEqual(str(request), expected_str)


class MSDSCodeRequestTest(TestCase):
    def setUp(self):
        self.country = Location.objects.create(
            name="Test Country",
            type="country"
        )
        self.solution = Solution.objects.create(
            name="Test Solution",
            commercial_name="Commercial Test Solution",
            description="Test description"
        )
        self.base_data = {
            'first_name': 'John',
            'last_name': 'Doe',
            'company_email_address': '<EMAIL>',
            'job_function': JobFunctionChoices.technology_manager,
            'company': 'Test Company',
            'country': self.country,
            'solution': self.solution,
        }

    def test_msds_specific_behavior(self):
        """Test MSDS specific behavior if any"""
        request = MSDSCodeRequest.objects.create(**self.base_data)
        self.assertIsInstance(request, MSDSCodeRequest)
        self.assertTrue(isinstance(request, BaseSolutionRequest))


class TDSCodeRequestTest(TestCase):
    def setUp(self):
        self.country = Location.objects.create(
            name="Test Country",
            type="country"
        )
        self.solution = Solution.objects.create(
            name="Test Solution",
            commercial_name="Commercial Test Solution",
            description="Test description"
        )
        self.base_data = {
            'first_name': 'John',
            'last_name': 'Doe',
            'company_email_address': '<EMAIL>',
            'job_function': JobFunctionChoices.technology_manager,
            'company': 'Test Company',
            'country': self.country,
            'solution': self.solution,
        }

    def test_tds_specific_behavior(self):
        """Test TDS specific behavior if any"""
        request = TDSCodeRequest.objects.create(**self.base_data)
        self.assertIsInstance(request, TDSCodeRequest)
        self.assertTrue(isinstance(request, BaseSolutionRequest))


class InquiryModelTest(TestCase):
    def setUp(self):
        self.country = Location.objects.create(
            name="Test Country",
            type="country"
        )
        self.solution = Solution.objects.create(
            name="Test Solution",
            commercial_name="Commercial Test Solution",
            description="Test description"
        )
        self.base_data = {
            'first_name': 'Jane',
            'last_name': 'Smith',
            'company_email_address': '<EMAIL>',
            'job_function': JobFunctionChoices.executive_director,
            'company': 'Test Company',
            'country': self.country,
            'solution': self.solution,
            'message': 'Test inquiry message'
        }

    def test_inquiry_message_required(self):
        """Test that message is required for Inquiry"""
        data = self.base_data.copy()
        del data['message']
        
        inquiry = Inquiry(**data)
        with self.assertRaises(ValidationError):
            inquiry.full_clean()

    def test_inquiry_message_can_be_long(self):
        """Test that message field can handle long text"""
        data = self.base_data.copy()
        data['message'] = 'x' * 1000  # Long message
        
        inquiry = Inquiry.objects.create(**data)
        self.assertEqual(inquiry.message, 'x' * 1000)

    def test_inquiry_relationships(self):
        """Test inquiry relationships with other models"""
        inquiry = Inquiry.objects.create(**self.base_data)
        
        # Test country relationship
        self.assertEqual(inquiry.country.name, "Test Country")
        self.assertEqual(inquiry.country.type, "country")
        
        # Test solution relationship
        self.assertEqual(inquiry.solution.name, "Test Solution")
        self.assertEqual(inquiry.solution.commercial_name, "Commercial Test Solution")

    def test_cascade_protection(self):
        """Test that related objects are protected from deletion"""
        inquiry = Inquiry.objects.create(**self.base_data)
        
        # Attempt to delete country should raise ProtectedError
        with self.assertRaises(ProtectedError):
            self.country.delete()
            
        # Attempt to delete solution should raise ProtectedError
        with self.assertRaises(ProtectedError):
            self.solution.delete()
