from django.db import models
from django.db.models import Q
from martor.models import Martor<PERSON><PERSON>

from core.models.fields.phone_number_field import PhoneNumberField


class Plant(models.Model):
    name = models.CharField(max_length=255)
    latitude = models.DecimalField(max_digits=13, decimal_places=10)
    longitude = models.DecimalField(max_digits=13, decimal_places=10)
    address = MartorField()
    country = models.ForeignKey(
        "core.Location",
        on_delete=models.PROTECT,
        limit_choices_to=Q(type="country"),
    )
    deprecated_solutions = models.ManyToManyField(
        "petrochemicals_catalog.Solution",
        related_name="deprecated_plants",
        db_table="petrochemicals_catalog_plant_solutions",
        blank=True
    )
    phone_number = PhoneNumberField(blank=True, null=True)
    email = models.EmailField(blank=True, null=True)
    website_url = models.URLField()
    linkedin_url = models.URLField(blank=True, null=True)

    def __str__(self):
        return self.name
