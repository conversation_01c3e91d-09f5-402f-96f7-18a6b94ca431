from rest_framework import serializers

from petrochemicals_catalog import models
from petrochemicals_catalog.api.serializers.solution import SolutionListSerializer
from social.serializers.is_bookmarked import BookmarkSerializer


class PlantDetailSerializer(serializers.ModelSerializer, BookmarkSerializer):
    # solutions = SolutionListSerializer(many=True)
    solutions = serializers.SerializerMethodField()

    def get_solutions(self, obj):
        queryset = models.Solution.objects.filter(
            processes__solutions__in=obj.solutions.values_list("id", flat=True),
        )
        return SolutionListSerializer(queryset, many=True).data

    class Meta:
        model = models.Plant
        fields = "__all__"


class PlantListSerializer(serializers.ModelSerializer, BookmarkSerializer):
    geographic_order = serializers.BooleanField(write_only=True, default=False)

    class Meta:
        model = models.Plant
        exclude = ["solutions"]
