from rest_framework import serializers

from core.api.serializers.image import ImageSerializer
from infrastructure.api.serializers.markdown_field import Markdown<PERSON>ield
from petrochemicals_catalog import models
from petrochemicals_catalog.api.serializers.category import CategoryListSerializer
from social.serializers.is_bookmarked import BookmarkSerializer


class SolutionListSerializer(serializers.ModelSerializer, BookmarkSerializer):
    image = ImageSerializer(read_only=True)
    description = MarkdownField()

    class Meta:
        model = models.Solution
        exclude = ["categories"]


class SolutionDetailSerializer(serializers.ModelSerializer, BookmarkSerializer):
    image = ImageSerializer(read_only=True)
    categories = CategoryListSerializer(many=True)
    description = MarkdownField()

    class Meta:
        model = models.Solution
        fields = "__all__"
