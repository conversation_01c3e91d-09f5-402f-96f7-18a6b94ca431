from rest_framework import routers

from petrochemicals_catalog.api.v1.category import CategoryViewSet
from petrochemicals_catalog.api.v1.plant import PlantViewSet
from petrochemicals_catalog.api.v1.process import ProcessViewSet
from petrochemicals_catalog.api.v1.solution import SolutionViewSet

router = routers.DefaultRouter()
router.register("categories", CategoryViewSet, basename="categories")
router.register("processes", ProcessViewSet, basename="processes")
router.register("plants", PlantViewSet, basename="plants")
router.register("solutions", SolutionViewSet, basename="solutions")
