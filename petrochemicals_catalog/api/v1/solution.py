from django_filters.rest_framework import DjangoFilterBackend
from drf_yasg import openapi
from drf_yasg.utils import swagger_auto_schema, no_body
from rest_framework import viewsets, status
from rest_framework.decorators import action
from rest_framework.filters import SearchFilter

from infrastructure.api.pagination.page_number_pagination import PageNumberPagination
from infrastructure.api.response.response import Response
from petrochemicals_catalog import models
from petrochemicals_catalog.api.filters.solution import SolutionFilter
from petrochemicals_catalog.api.serializers.inquiry import InquirySerializer
from petrochemicals_catalog.api.serializers.msds_code_request import MSDSCodeRequestSerializer
from petrochemicals_catalog.api.serializers.solution import SolutionListSerializer
from petrochemicals_catalog.api.serializers.tds_code_request import TDSCodeRequestSerializer
from petrochemicals_catalog.models.base_solution_request import JobFunctionChoices
from social.api.bookmark_model_mixin import BookmarkModelMixin


class SolutionViewSet(viewsets.ReadOnlyModelViewSet, BookmarkModelMixin):
    queryset = models.Solution.objects.prefetch_related(
        'categories',
    ).order_by(
        'id',
    )
    filter_backends = [DjangoFilterBackend, SearchFilter]
    filterset_class = SolutionFilter
    search_fields = ["name"]
    pagination_class = PageNumberPagination
    serializer_class = SolutionListSerializer

    @swagger_auto_schema(
        method='post',
        operation_description="Submit an inquiry for a specific solution",
        request_body=InquirySerializer,
        responses={
            status.HTTP_201_CREATED: openapi.Response(
                description="Inquiry created successfully",
                schema=InquirySerializer
            ),
            status.HTTP_400_BAD_REQUEST: "Invalid request data"
        }
    )
    @action(methods=['POST'], detail=True)
    def inquiry(self, request, *args, **kwargs):
        instance = self.get_object()
        serializer = InquirySerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        serializer.save(solution=instance)
        return Response(serializer.data, status=status.HTTP_201_CREATED, message="Inquiry sent successfully.")

    @swagger_auto_schema(
        method='post',
        operation_description="Request MSDS code for a specific solution",
        request_body=MSDSCodeRequestSerializer,
        responses={
            status.HTTP_201_CREATED: openapi.Response(
                description="MSDS code request created successfully",
                schema=MSDSCodeRequestSerializer
            ),
            status.HTTP_400_BAD_REQUEST: "Invalid request data"
        }
    )
    @action(methods=['POST'], detail=True)
    def msds_code_request(self, request, *args, **kwargs):
        instance = self.get_object()
        serializer = MSDSCodeRequestSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        serializer.save(solution=instance)
        return Response(serializer.data, status=status.HTTP_201_CREATED, message="MSDS code request sent successfully.")

    @swagger_auto_schema(
        method='post',
        operation_description="Request TDS code for a specific solution",
        request_body=TDSCodeRequestSerializer,
        responses={
            status.HTTP_201_CREATED: openapi.Response(
                description="TDS code request created successfully",
                schema=TDSCodeRequestSerializer
            ),
            status.HTTP_400_BAD_REQUEST: "Invalid request data"
        }
    )
    @action(methods=['POST'], detail=True)
    def tds_code_request(self, request, *args, **kwargs):
        instance = self.get_object()
        serializer = TDSCodeRequestSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        serializer.save(solution=instance)
        return Response(serializer.data, status=status.HTTP_201_CREATED, message="TDS code request sent successfully.")

    @swagger_auto_schema(
        method='get',
        operation_description="Get a list of all available job functions",
        request_body=no_body,
        responses={
            200: openapi.Response(
                description="A list of job functions",
                schema=openapi.Schema(
                    type=openapi.TYPE_ARRAY,
                    items=openapi.Schema(
                        type=openapi.TYPE_OBJECT,
                        properties={
                            'id': openapi.Schema(type=openapi.TYPE_STRING),
                            'name': openapi.Schema(type=openapi.TYPE_STRING),
                        }
                    )
                )
            )
        }
    )
    @action(methods=['GET'], detail=False)
    def job_functions(self, request, *args, **kwargs):
        result = []
        for item in JobFunctionChoices.choices:
            result.append({
                'id': item[0],
                'name': item[1],
            })
        return Response(data=result)
