from django_filters.rest_framework import DjangoFilterBackend
from rest_framework import viewsets

from infrastructure.api.pagination.page_number_pagination import PageNumberPagination
from petrochemicals_catalog import models
from petrochemicals_catalog.api.filters.plant import PlantFilter
from petrochemicals_catalog.api.serializers.plant import PlantListSerializer, PlantDetailSerializer
from social.api.bookmark_model_mixin import BookmarkModelMixin


class PlantViewSet(BookmarkModelMixin, viewsets.ReadOnlyModelViewSet):
    pagination_class = PageNumberPagination
    filter_backends = [DjangoFilterBackend]
    filterset_class = PlantFilter

    def get_queryset(self):
        queryset = models.Plant.objects.order_by(
            'id',
        )

        geographic_order = self.request.query_params.get('geographic_order', 'false').lower() == 'true'
        if geographic_order:
            return queryset.order_by('longitude')

        return queryset

    def get_serializer_class(self):
        if self.action == "retrieve":
            return PlantDetailSerializer
        return PlantListSerializer
