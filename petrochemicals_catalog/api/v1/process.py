from django_filters.rest_framework import DjangoFilterBackend
from rest_framework import viewsets

from petrochemicals_catalog import models
from petrochemicals_catalog.api.filters.process import ProcessFilter
from petrochemicals_catalog.api.serializers.process import ProcessSerializer


class ProcessViewSet(viewsets.ReadOnlyModelViewSet):
    filter_backends = [DjangoFilterBackend]
    filterset_class = ProcessFilter
    serializer_class = ProcessSerializer

    def get_queryset(self):
        queryset = models.Process.objects.order_by(
            'id',
        )

        has_solution = self.request.query_params.get('has_solution', 'false').lower() == 'true'
        if has_solution:
            queryset = queryset.exclude(
                solutions__isnull=True,
            ).distinct()

        has_plant = self.request.query_params.get('has_plant', 'false').lower() == 'true'
        if has_plant:
            queryset = queryset.exclude(
                plants__isnull=True,
            ).distinct()

        return queryset
