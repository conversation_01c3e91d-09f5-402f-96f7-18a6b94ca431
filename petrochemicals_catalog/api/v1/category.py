from django.db.models import Q
from rest_framework import viewsets

from petrochemicals_catalog import models
from petrochemicals_catalog.api.serializers.category import CategoryListSerializer


class CategoryViewSet(viewsets.ReadOnlyModelViewSet):
    serializer_class = CategoryListSerializer

    def get_queryset(self):
        queryset = models.Category.objects.filter(
            parent_id__isnull=True,
        ).order_by(
            'id',
        )

        has_solution = self.request.query_params.get('has_solution', 'false').lower() == 'true'

        if has_solution:
            queryset = queryset.filter(
                Q(solution__isnull=False) | Q(children__isnull=False),
            ).distinct()

        return queryset
