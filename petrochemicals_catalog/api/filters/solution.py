from django_filters import rest_framework as filters

from petrochemicals_catalog import models


class SolutionFilter(filters.FilterSet):
    plants = filters.ModelMultipleChoiceFilter(method="filter_plants", queryset=models.Plant.objects.all())

    def filter_plants(self, queryset, field_name, value):
        print(value)
        return queryset.filter(
            processes__plants__in=value,
        )

    class Meta:
        model = models.Solution
        fields = ['categories', 'processes']
