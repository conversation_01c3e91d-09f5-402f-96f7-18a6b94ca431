from rest_framework import routers

from job_board.api.v1.application import ApplicationViewSet
from job_board.api.v1.department import DepartmentViewSet
from job_board.api.v1.vacancy import VacancyViewSet

router = routers.DefaultRouter()
router.register("departments", DepartmentViewSet, basename="departments")
router.register("vacancies", VacancyViewSet, basename="vacancies")
router.register("applications", ApplicationViewSet, basename="applications")
